{"name": "nft-marketplace", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "compile": "hardhat compile", "deploy:local": "hardhat run scripts/deploy.js --network hardhat", "deploy:sepolia": "hardhat run scripts/deploy.js --network sepolia", "deploy:goerli": "hardhat run scripts/deploy.js --network goerli", "deploy:social": "node scripts/deploy-social-features.js"}, "dependencies": {"@headlessui/react": "^1.7.18", "@heroicons/react": "^2.1.1", "@nomiclabs/hardhat-etherscan": "^3.1.8", "@openzeppelin/contracts": "^5.4.0", "@supabase/supabase-js": "^2.49.4", "axios": "^1.6.5", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "ethers": "^5.8.0", "hardhat-gas-reporter": "^2.3.0", "isomorphic-dompurify": "^2.26.0", "next": "14.1.0", "node-fetch": "^3.3.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.0.1", "react-toastify": "^10.0.4"}, "devDependencies": {"@nomiclabs/hardhat-ethers": "^2.2.3", "@nomiclabs/hardhat-waffle": "^2.0.6", "@types/node": "^20.11.5", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "browserify-zlib": "^0.2.0", "chai": "^4.3.10", "critters": "^0.0.23", "crypto-browserify": "^3.12.0", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "ethereum-waffle": "^4.0.10", "hardhat": "^2.19.4", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "postcss": "^8.4.33", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}