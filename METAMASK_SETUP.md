# 🦊 MetaMask Setup Guide for NFT Marketplace

This guide will help you install and configure MetaMask to use with our NFT marketplace.

## 📋 What is MetaMask?

MetaMask is a cryptocurrency wallet and gateway to blockchain applications. It allows you to:

- **Store & Manage Crypto**: Securely store Ethereum and other cryptocurrencies
- **Interact with dApps**: Connect to decentralized applications like our NFT marketplace
- **Buy, Sell & Trade NFTs**: Participate in the NFT ecosystem
- **Sign Transactions**: Securely authorize blockchain transactions

## 🚀 Installation Steps

### Step 1: Download MetaMask

1. Visit [metamask.io](https://metamask.io/download/)
2. Click "Download for [Your Browser]"
3. Add the extension to your browser
4. Pin the extension to your browser toolbar

### Step 2: Create or Import Wallet

#### For New Users:
1. Click "Create a Wallet"
2. Create a strong password
3. **IMPORTANT**: Write down your seed phrase and store it securely
4. Confirm your seed phrase
5. Your wallet is ready!

#### For Existing Users:
1. Click "Import Wallet"
2. Enter your 12-word seed phrase
3. Create a new password
4. Your wallet is restored!

### Step 3: Connect to Network

1. Open MetaMask extension
2. Click the network dropdown (top center)
3. Select your preferred network:
   - **Ethereum Mainnet** (for real transactions)
   - **Sepolia Testnet** (for testing with free ETH)

### Step 4: Get Test ETH (For Testing)

If using Sepolia testnet:
1. Visit [Sepolia Faucet](https://sepoliafaucet.com/)
2. Enter your wallet address
3. Request test ETH
4. Wait for the transaction to complete

## 🔧 Configuration for Our Marketplace

### Network Settings

Our marketplace supports:
- **Ethereum Mainnet** (Chain ID: 1)
- **Sepolia Testnet** (Chain ID: ********)

### Required Permissions

When connecting to our marketplace, MetaMask will request:
- **Account Access**: To view your wallet address
- **Transaction Signing**: To mint and trade NFTs
- **Network Switching**: To change networks if needed

## 🛡️ Security Best Practices

### Seed Phrase Security
- **Never share** your seed phrase with anyone
- **Store offline** in a secure location
- **Use multiple backups** in different locations
- **Never enter** your seed phrase on suspicious websites

### Transaction Safety
- **Always verify** transaction details before signing
- **Check recipient addresses** carefully
- **Be aware of gas fees** before confirming
- **Use test networks** for learning and testing

### Phishing Protection
- **Bookmark** the official MetaMask extension
- **Verify URLs** before entering sensitive information
- **Be suspicious** of unsolicited messages or emails
- **Use official links** only

## 🔗 Connecting to Our Marketplace

1. **Visit our marketplace** in your browser
2. **Click "Connect Wallet"** on any page requiring wallet access
3. **Select MetaMask** from the wallet options
4. **Approve the connection** in the MetaMask popup
5. **Sign the connection message** to verify ownership
6. **You're connected!** Your wallet address will appear in the interface

## 🎨 Using the Marketplace

### Creating NFTs
1. **Connect your wallet** (as described above)
2. **Navigate to "Create"** page
3. **Upload your artwork** and fill in details
4. **Set your price** and royalty percentage
5. **Click "Mint NFT"** and confirm the transaction
6. **Wait for confirmation** on the blockchain

### Buying NFTs
1. **Browse the marketplace** for NFTs you like
2. **Click "Buy Now"** on an NFT
3. **Review the transaction** details
4. **Confirm the purchase** in MetaMask
5. **Wait for confirmation** - the NFT will appear in your wallet

### Selling NFTs
1. **Go to your profile** or dashboard
2. **Select an NFT** you own
3. **Click "List for Sale"**
4. **Set your price** and confirm
5. **Sign the listing transaction** in MetaMask

## 🆘 Troubleshooting

### Common Issues

#### "MetaMask is not installed"
- **Solution**: Install MetaMask browser extension from [metamask.io](https://metamask.io)
- **Refresh** the page after installation

#### "Wrong Network"
- **Solution**: Switch to Ethereum Mainnet or Sepolia Testnet in MetaMask
- **Click** the network dropdown and select the correct network

#### "Insufficient Funds"
- **Solution**: Add ETH to your wallet
- **For Mainnet**: Buy ETH from an exchange
- **For Testnet**: Use a faucet to get free test ETH

#### "Transaction Failed"
- **Solution**: Check gas fees and try again
- **Increase** gas limit if necessary
- **Wait** for network congestion to clear

#### "Connection Issues"
- **Solution**: Disconnect and reconnect your wallet
- **Clear** browser cache and cookies
- **Restart** your browser

### Getting Help

If you need additional support:
1. **Check our FAQ** section
2. **Contact our support** team
3. **Visit MetaMask Help Center**: [support.metamask.io](https://support.metamask.io)
4. **Join our Discord** community for peer support

## 🔄 Alternative Wallets (Coming Soon)

While MetaMask is currently required, we're working on supporting:
- **WalletConnect** (mobile wallets)
- **Coinbase Wallet**
- **Trust Wallet**
- **Rainbow Wallet**

## 📱 Mobile Usage

MetaMask is also available on mobile:
1. **Download** MetaMask app from App Store/Google Play
2. **Import** your existing wallet or create new
3. **Use** the in-app browser to access our marketplace
4. **Connect** your wallet as described above

---

**Need Help?** Contact our support team or check the troubleshooting section above.

**Security Notice**: Always verify you're on the correct website before connecting your wallet!
